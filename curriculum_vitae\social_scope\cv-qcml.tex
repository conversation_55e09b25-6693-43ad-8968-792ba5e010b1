\documentclass[a4paper,skipsamekey,11pt,english]{curve}
\usepackage{ctex}
\usepackage{settings}
\usepackage{enumitem} 
% Change the fonts if you want
\ifxetexorluatex % If you're using XeLaTeX or LuaLaTeX
  \usepackage{fontspec} 
  %% You can use \setmainfont etc; I'm just using these font packages here because they provide OpenType fonts for use by XeLaTeX/LuaLaTeX anyway
  \usepackage[p,osf,swashQ]{cochineal}
  \usepackage[medium,bold]{cabin}
  \usepackage[varqu,varl,scale=0.9]{zi4}
\else % If you're using pdfLaTeX or latex
  \usepackage[T1]{fontenc}
  \usepackage[p,osf,swashQ]{cochineal}
  \usepackage{cabin}
  \usepackage[varqu,varl,scale=0.9]{zi4}
\fi
\addbibresource{cite-self.bib}
\myname{<PERSON>}{Xiao\bibnamedeli<PERSON> Xu}
\definecolor{SwishLineColour}{HTML}{236B8E}
\includecomment{fullonly}
\leftheader{%
  {\LARGE\bfseries\sffamily 张晓旭, 物理学博士}

  \makefield{\faMobile[regular]}{\texttt{18910752106}}  


  \makefield{\faEnvelope[regular]}{\texttt{<EMAIL>}}

  
  \makefield{\faGithub}{\url{https://github.com/longwarriors}}

}

\rightheader{~}
\begin{fullonly}
\photo[r]{photo}
\photoscale{0.13}
\end{fullonly}
\title{Curriculum Vitae}
\begin{document}
\makeheaders[c]
\makerubric{education}
\makerubric{employment}
\makerubric{experience}
\makerubric{skills}
\input{publications}
\end{document}