%!TEX encoding = UTF8
%!TEX root =cv-llt.tex

\begin{rubric}{项目经历}
  % \noentry{2014 -- 2015}  
  % \entry*[金融时序数据的高级混合建模与算法优化\hfill]
  % 针对分钟级加密货币价格预测问题，设计并实现了一套基于混合建模和集成学习的高级算法解决方案。
  \entry*[量子机器学习\hfill]
  研究量子机器学习范式，自研并投产了基于Pytorch深度学习框架的量子线路模拟器。
  开发了量子核方法用于金融风险分析。
  开发了量子长短时记忆网络用于金融时序数据分析。  
  \entry*[量子金融算法\hfill]
  开发了期权定价算法、投资组合近似优化等适配超导量子计算机运行的算法。并自研GPU加速的QUBO求解器来模拟运行QAOA算法。 
  \entry*[金融AI云平台\hfill]
  投产上线AI量化平台，负责应用架构、部署架构和安全架构。
  开发针对非结构化文本数据的BERT情绪因子提取模型。
  开发含权债券定价的BDT模型。
  开发上线一个用于文档摘要的大语言模型智能体。
  \entry*[毫米波波瓣对准\hfill]
  利用IMU单元与GNSS单元控制机械转台，对发射机与接收机的卡塞格伦天线进行视距对准，保持高吞吐量的通信链接。
  \entry*[图形编程]
  编写游戏引擎中显卡的图形管线，包括渲染方程的pixel shader和物理模拟的compute shader。
\end{rubric}
