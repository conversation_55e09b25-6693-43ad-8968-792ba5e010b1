\begin{rubric}{项目经历}
  \subrubric{智能信贷风控模型与多源数据融合分析}
  \entry*[2022] \textbf{项目背景：}该项目旨在通过机器学习技术，精准预测客户贷款违约风险。项目核心挑战在于如何利用多维度的匿名异构数据，构建高精度的风险预测模型。
  %
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 整合多信源的数百万条数据表（包括客户申请信息、征信局历史数据、历史贷款记录、信用卡月度账单和匿名特征等），处理包含数百个原始特征的复杂数据集。
    \item 设计并实施了多种缺失值填充策略、异常值检测与处理，以及数据类型优化，将数据处理阶段的内存占用降低了 60\%。
          % \item 将多个来源的非结构化和半结构化数据进行连接与对齐，构建了一个统一的、可供模型使用的宽表视图。
    \item 总计构建了超过500个具有强预测能力的衍生特征，使模型的 AUC (Area Under ROC Curve) 相比基线模型提升了 15\%。
          % \item 构建了以 LightGBM 为核心的梯度提升决策树模型，并通过贝叶斯优化进行超参数调优，实现了模型性能的最优化。
    \item 实施了模型集成策略（Ensemble Learning），融合了多个模型的预测结果，进一步提升了模型的稳定性和泛化能力，最终取得了优异的预测效果。
  \end{enumerate}
  \entry*[] \textbf{项目成果：}构建了一个可配置、可扩展且易于维护的机器学习流水线，在海量测试样本上表现出色，AUC分数达到0.8+。
  %
  %
  \subrubric{分钟级金融时序数据的资产价格预测系统}
  \entry*[2023] \textbf{项目背景：}为分钟级的资产市场价格预测场景，构建了一个稳健、可扩展且自动化的机器学习预测管道。核心目标在于深度挖掘数据的时间依赖性与非线性特征关系，并通过复杂的模型集成技术挑战预测性能的极限。
  %
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 清洗高频交易数据，处理缺失值和异常值，构建标准化的时序特征工程流水线。处理超过50GB的大规模时序数据，通过数据类型优化和分块处理降低内存占用。
    \item 设计并构建了一套丰富的特征体系，包括滞后特征、滑动窗口统计特征（均值、波动率）以及指数加权移动平均 (EWMA)，有效捕捉了市场的多维度动态。
    \item 构建了混合建模框架，融合了 GBDT 模型 (LightGBM/XGBoost) 和深度学习模型 (LSTM) 的优势，分别用于捕捉数据中的截面特征关系和时间序列依赖。
    \item 采用 Stacking 集成策略，将不同基模型的预测结果作为元特征，训练一个最终的元学习器，显著提升了预测的准确性和鲁棒性。
  \end{enumerate}
  %
  \entry*[] \textbf{项目成果：}通过精细的特征工程和高级集成策略，模型性能监控的皮尔逊相关系数提升30\%。并成功构建一个端到端的预测系统，将从接收新数据到产出预测结果的周期从数小时缩短至30分钟以内。
  %
  %
  \subrubric{金融AI云平台}
  \entry*[2022] \textbf{项目背景：}
  \entry*[] \textbf{项目成果：}

  \subrubric{量子近似优化算法在投资组合优化中的应用}
  \entry*[2022] \textbf{项目背景：}
  \entry*[] \textbf{项目成果：}

  \subrubric{量子模拟器}
  \entry*[2022] \textbf{项目背景：}
  \entry*[] \textbf{项目成果：}

\end{rubric}