%% Sometimes when a section can't be nicely modelled with the \entry[]... mechanism; hack our own
\makerubrichead{论文发表}

%% Assuming you've already given \addbibresource{own-bib.bib} in the main doc. Right? Right???
\nocite{*}

%% If you just want everything in one list
% \printbibliography[heading={none}]

\printbibliography[heading={subbibliography},title={Journal Articles \faIcon{file-alt}},type=article]

\printbibliography[heading={subbibliography},title={Conference Proceedings},type=inproceedings]

\printbibliography[heading={subbibliography},title={Books and Chapters},filter={booksandchapters}]

% \printbibliography[heading=subbibliography]