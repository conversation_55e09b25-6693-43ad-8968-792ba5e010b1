% This CV example/template is based on my own
% CV which I (lamely attempted) to clean up, so that
% it's less of an eyesore and easier for others to use.
%
% LianTze <PERSON> (<EMAIL>)
% 13 May, 2020
%
\documentclass[a4paper,skipsamekey,11pt,english]{curve}

% Uncomment to enable Chinese; needs XeLaTeX
\usepackage{ctex}

\usepackage{settings}


% Change the fonts if you want
\ifxetexorluatex % If you're using XeLaTeX or LuaLaTeX
  \usepackage{fontspec} 
  %% You can use \setmainfont etc; I'm just using these font packages here because they provide OpenType fonts for use by XeLaTeX/LuaLaTeX anyway
  \usepackage[p,osf,swashQ]{cochineal}
  \usepackage[medium,bold]{cabin}
  \usepackage[varqu,varl,scale=0.9]{zi4}
\else % If you're using pdfLaTeX or latex
  \usepackage[T1]{fontenc}
  \usepackage[p,osf,swashQ]{cochineal}
  \usepackage{cabin}
  \usepackage[varqu,varl,scale=0.9]{zi4}
\fi

%% Only needed if you want a Publication List
\addbibresource{cite-self.bib}

%% Specify your last name and first name (as given in the .bib) to automatically bold your own name in the publications list. One caveat: You need to write \bibnamedelima where there's a space in your name for this to work properly for now...
\myname{Zhang}{Xiao\bibnamedelima Xu}
% \myname{d'Andrimont}{Raphaël}

% Change the page margins if you want
% \geometry{left=1cm,right=1cm,top=1.5cm,bottom=1.5cm}

% Change the colours if you want
\definecolor{SwishLineColour}{HTML}{236B8E}
% \definecolor{MarkerColour}{HTML}{0000CC}

% Change the item prefix marker if you want
% \prefixmarker{$\diamond$}

%% Photo is only shown if "fullonly" is included
\includecomment{fullonly}
% \excludecomment{fullonly}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


\leftheader{%
  {\LARGE\bfseries\sffamily 张晓旭, 物理学博士}

  \makefield{\faMobile[regular]}{\texttt{18910752106}}  \makefield{\faEnvelope[regular]}{\texttt{<EMAIL>}}

  \makefield{\faZhihu}{\url{https://www.zhihu.com/people/opticszhang}}

  \makefield{\faGithub}{\url{https://github.com/SchroedingerDog}}

}

\rightheader{~}
\begin{fullonly}
\photo[r]{photo}
\photoscale{0.13}
\end{fullonly}

\title{Curriculum Vitae}

\begin{document}

\makeheaders[c]

\makerubric{education}

\makerubric{employment}

\makerubric{projects}

\makerubric{skills}

% If you're not a researcher nor an academic, you probably don't have any publications; delete this line.
%% Sometimes when a section can't be nicely modelled with the \entry[]... mechanism; hack our own and use \input NOT \makerubric
\input{publications}

\end{document}