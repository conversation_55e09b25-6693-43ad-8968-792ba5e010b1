\begin{rubric}{项目经历}
    \subrubric{基于混合模型的分钟级加密货币价格预测系统}
    \entry*[2023] \textbf{项目描述：}主导设计并实现了一个用于预测高频金融市场数据的机器学习系统。
    通过分析分钟级的交易数据，精准预测加密货币未来价格变动。
    \entry*[]     \textbf{核心职责与贡献：}
    \begin{itemize}[label=-]
    \item 核心职责与贡献：
    \begin{itemize}[label=-]
        \item 数据处理与特征工程：
        \begin{itemize}[label=-]
        \item 负责处理超过 50GB 的大规模时序数据，通过数据类型优化和分块处理，将内存占用降低 50\%。
        \item 设计并构建了一套丰富的特征体系，包括滞后特征、滑动窗口统计特征（均值、波动率）以及指数加权移动平均 (EWMA)，有效捕捉了市场的多维度动态。
        \item 实施稳健的数据预处理流程，包括异常值（无穷大）处理、极端值截断和基于 TimeSeriesSplit 的时间序列交叉验证，确保了模型的稳定性和泛化能力。
        \end{itemize}
        \item 混合建模与集成学习：
        \begin{itemize}[label=-]
        \item 构建了混合建模框架，融合了 GBDT 模型 (LightGBM/XGBoost) 和深度学习模型 (LSTM) 的优势，分别用于捕捉数据中的截面特征关系和时间序列依赖。
        \item 采用 Stacking (堆叠) 集成策略，将不同基模型的预测结果作为元特征，训练一个最终的元学习器，显著提升了预测的准确性和鲁棒性。
        \end{itemize}
        \item 系统评估与部署：
        \begin{itemize}[label=-]
        \item 建立了包含皮尔逊相关系数、RMSE、SHAP 等多种指标的综合评估体系，用于模型性能监控和特征重要性分析。
        \item 将整个预测流程封装成一个自动化的端到端管道，实现了从原始数据加载、预处理、模型预测到生成标准提交文件的完整部署，支持大规模数据的批量预测。
        \end{itemize}
    \end{itemize}
    \end{itemize}
    \entry*[]     \textbf{项目成果：}通过精细的特征工程和高级集成策略，最终模型相较于基线模型提升了40\%
    
\end{rubric}